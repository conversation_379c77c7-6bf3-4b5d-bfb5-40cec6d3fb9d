import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
from matplotlib.patches import Rectangle
import pandas as pd

def create_academic_gantt_chart():
    """Create an academic-style Gantt chart matching the revised WP structure"""

    fig, ax = plt.subplots(figsize=(18, 8))

    # Define activities with new WP numbering
    activities = [
        # WP1 Activities - Light Blue
        {'code': 'WP1.1', 'wp': 'WP1', 'start': 1, 'duration': 2, 'color': '#E3F2FD'},
        {'code': 'WP1.2', 'wp': 'WP1', 'start': 1, 'duration': 4, 'color': '#E3F2FD'},
        {'code': 'WP1.3', 'wp': 'WP1', 'start': 2, 'duration': 2, 'color': '#E3F2FD'},
        {'code': 'WP1.4', 'wp': 'WP1', 'start': 2, 'duration': 2, 'color': '#E3F2FD'},
        {'code': 'WP1.5', 'wp': 'WP1', 'start': 4, 'duration': 3, 'color': '#E3F2FD'},

        # WP2 Activities - Light Green
        {'code': 'WP2.1', 'wp': 'WP2', 'start': 3, 'duration': 3, 'color': '#E8F5E8'},
        {'code': 'WP2.2', 'wp': 'WP2', 'start': 4, 'duration': 4, 'color': '#E8F5E8'},
        {'code': 'WP2.3', 'wp': 'WP2', 'start': 6, 'duration': 4, 'color': '#E8F5E8'},
        {'code': 'WP2.4', 'wp': 'WP2', 'start': 7, 'duration': 3, 'color': '#E8F5E8'},
        {'code': 'WP2.5', 'wp': 'WP2', 'start': 9, 'duration': 4, 'color': '#E8F5E8'},
        {'code': 'WP2.6', 'wp': 'WP2', 'start': 11, 'duration': 2, 'color': '#E8F5E8'},

        # WP3 Activities - Light Orange
        {'code': 'WP3.1', 'wp': 'WP3', 'start': 10, 'duration': 3, 'color': '#FFF3E0'},
        {'code': 'WP3.2', 'wp': 'WP3', 'start': 12, 'duration': 4, 'color': '#FFF3E0'},
        {'code': 'WP3.3', 'wp': 'WP3', 'start': 14, 'duration': 4, 'color': '#FFF3E0'},
        {'code': 'WP3.4', 'wp': 'WP3', 'start': 16, 'duration': 3, 'color': '#FFF3E0'},
        {'code': 'WP3.5', 'wp': 'WP3', 'start': 17, 'duration': 2, 'color': '#FFF3E0'},

        # WP4 Activities - Light Purple
        {'code': 'WP4.1', 'wp': 'WP4', 'start': 16, 'duration': 3, 'color': '#F3E5F5'},
        {'code': 'WP4.2', 'wp': 'WP4', 'start': 17, 'duration': 3, 'color': '#F3E5F5'},
        {'code': 'WP4.3', 'wp': 'WP4', 'start': 19, 'duration': 3, 'color': '#F3E5F5'},
        {'code': 'WP4.4', 'wp': 'WP4', 'start': 20, 'duration': 3, 'color': '#F3E5F5'},
        {'code': 'WP4.5', 'wp': 'WP4', 'start': 21, 'duration': 2, 'color': '#F3E5F5'},

        # WP5 Activities - Light Pink
        {'code': 'WP5.1', 'wp': 'WP5', 'start': 6, 'duration': 19, 'color': '#FCE4EC'},
        {'code': 'WP5.2', 'wp': 'WP5', 'start': 12, 'duration': 4, 'color': '#FCE4EC'},
        {'code': 'WP5.3', 'wp': 'WP5', 'start': 18, 'duration': 4, 'color': '#FCE4EC'},
        {'code': 'WP5.4', 'wp': 'WP5', 'start': 22, 'duration': 3, 'color': '#FCE4EC'},
        {'code': 'WP5.5', 'wp': 'WP5', 'start': 20, 'duration': 5, 'color': '#FCE4EC'},
        {'code': 'WP5.6', 'wp': 'WP5', 'start': 23, 'duration': 2, 'color': '#FCE4EC'},
    ]

    # Deliverables
    deliverables = [
        {'name': 'D1.1', 'month': 3, 'description': 'Career Development Plan'},
        {'name': 'D1.2', 'month': 6, 'description': 'Data Management Plan'},
        {'name': 'D2.1', 'month': 12, 'description': 'Validation Report & GW Climatology'},
        {'name': 'D3.1', 'month': 18, 'description': 'MJO-GW Climatology Report'},
        {'name': 'D4.1', 'month': 22, 'description': 'Model Evaluation Report'},
        {'name': 'D5.1', 'month': 24, 'description': 'Final Project Report'}
    ]

    # Plotting
    y_positions = np.arange(len(activities))[::-1]

    for i, activity in enumerate(activities):
        y_pos = len(activities) - 1 - i
        rect = Rectangle((activity['start']-1, y_pos-0.35), activity['duration'], 0.9,
                        facecolor=activity['color'], edgecolor='black', linewidth=0.8)
        ax.add_patch(rect)
        ax.text(activity['start'] - 1 + activity['duration']/2, y_pos,
                activity['code'], ha='center', va='center',
                fontsize=12, fontweight='bold', color='black')

    # Deliverable markers
    for deliv in deliverables:
        ax.scatter(deliv['month'], -0.8, s=120, marker='D',
                  color='red', edgecolor='black', linewidth=1, zorder=10)
        ax.text(deliv['month'], -1.1, deliv['name'],
               ha='center', va='top', fontsize=12, fontweight='bold', color='red')

    # Formatting
    ax.set_xlim(0, 25)
    ax.set_ylim(-1.5, len(activities) + 0.5)
    ax.set_ylabel('Work Packages', fontsize=16, fontweight='bold')
    # ax.set_title('MJO Stratospheric Gravity Waves Project - 24 Month Timeline',
    #             fontsize=20, fontweight='bold', pad=30)
    ax.set_yticks(y_positions)
    ax.set_yticklabels([a['code'] for a in activities], fontsize=14, fontweight='bold')

    # Set x-axis ticks for months at TOP
    months = np.arange(1, 25)
    ax.set_xticks(months)
    ax.set_xticklabels(
        [f'M{m}' for m in months],
        rotation=0,
        fontsize=12,
        fontweight='bold'   # <-- MAKE BOLD
    )
    ax.xaxis.tick_top()  # Move x-axis to top
    ax.xaxis.set_label_position('top')
    
    # Increase spacing between label and ticks
    ax.set_xlabel(
        'Project Timeline (Months)',
        fontsize=16,
        fontweight='bold',
        labelpad=20   # <-- add extra padding
    )

    ax.grid(True, axis='x', alpha=0.8, linestyle='-', linewidth=0.5)
    ax.set_axisbelow(True)

    for q in [6, 12, 18, 24]:
        ax.axvline(x=q, color='darkblue', linestyle='--', alpha=0.6, linewidth=2)
        ax.text(q, len(activities) + 0.2, f'Q{q//6}', ha='center', va='bottom',
                fontweight='bold', color='darkblue', fontsize=14,
                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', edgecolor='darkblue'))

    plt.tight_layout()
    plt.savefig('MJO_Academic_Gantt_Chart.jpeg', dpi=300, bbox_inches='tight')
    plt.savefig('MJO_Academic_Gantt_Chart.pdf', bbox_inches='tight')

    return fig, ax


if __name__ == "__main__":
    fig, ax = create_academic_gantt_chart()
    plt.show()
