This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: MJO_proposal_template.aux
Reallocating 'name_of_file' (item size: 1) to 9 items.
The style file: unsrtnat.bst
Reallocating 'name_of_file' (item size: 1) to 24 items.
Database file #1: MJO_proposal_references.bib
You've used 42 entries,
            2481 wiz_defined-function locations,
            854 strings with 14762 characters,
and the built_in function-call counts, 19791 in all, are:
= -- 1620
> -- 1288
< -- 15
+ -- 475
- -- 390
* -- 1846
:= -- 3286
add.period$ -- 171
call.type$ -- 42
change.case$ -- 84
chr.to.int$ -- 41
cite$ -- 42
duplicate$ -- 816
empty$ -- 1612
format.name$ -- 448
if$ -- 4071
int.to.chr$ -- 2
int.to.str$ -- 43
missing$ -- 42
newline$ -- 261
num.names$ -- 126
pop$ -- 262
preamble$ -- 1
purify$ -- 42
quote$ -- 0
skip$ -- 467
stack$ -- 0
substring$ -- 1106
swap$ -- 106
text.length$ -- 3
text.prefix$ -- 0
top$ -- 0
type$ -- 294
warning$ -- 0
while$ -- 148
width$ -- 0
write$ -- 641
