#!/usr/bin/env python3
"""
Enhanced LLNL Lawrence Fellowship Proposal Generator
Incorporates E3SM integration and scientific improvements
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn

def add_superscript_reference(paragraph, ref_num):
    """Add superscript reference number to paragraph"""
    run = paragraph.add_run(str(ref_num))
    run.font.superscript = True
    run.font.size = Pt(8)

def create_enhanced_proposal():
    """Create the enhanced LLNL proposal document"""
    
    # Create document
    doc = Document()
    
    # Set document margins
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(0.8)
        section.bottom_margin = Inches(0.8)
        section.left_margin = Inches(0.8)
        section.right_margin = Inches(0.8)
    
    # Configure styles
    styles = doc.styles
    
    # Normal style
    normal_style = styles['Normal']
    normal_font = normal_style.font
    normal_font.name = 'Times New Roman'
    normal_font.size = Pt(10)
    
    # Paragraph formatting
    normal_paragraph = normal_style.paragraph_format
    normal_paragraph.line_spacing = 1.15
    normal_paragraph.space_after = Pt(6)
    
    # Heading styles
    heading1_style = styles['Heading 1']
    heading1_font = heading1_style.font
    heading1_font.name = 'Times New Roman'
    heading1_font.size = Pt(12)
    heading1_font.bold = True
    
    heading2_style = styles['Heading 2']
    heading2_font = heading2_style.font
    heading2_font.name = 'Times New Roman'
    heading2_font.size = Pt(11)
    heading2_font.bold = True
    
    # Title
    title = doc.add_heading('', level=0)
    title_run = title.runs[0] if title.runs else title.add_run()
    title_run.text = 'Modulation of Stratospheric Gravity Waves by the Madden-Julian Oscillation: Observational Constraints for E3SM Model Development and Enhanced Climate Prediction'
    title_run.font.name = 'Times New Roman'
    title_run.font.size = Pt(14)
    title_run.font.bold = True
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Author info
    author_para = doc.add_paragraph()
    author_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    author_run = author_para.add_run('Dr. Toyese Tunde Ayorinde\nLawrence Fellowship Research Proposal\nLawrence Livermore National Laboratory\nAtmospheric, Earth, and Energy Division')
    author_run.font.name = 'Times New Roman'
    author_run.font.size = Pt(11)
    
    # Add spacing
    doc.add_paragraph()
    
    return doc

def add_background_section(doc):
    """Add the Background and Motivation section"""
    
    # Background and Motivation
    bg_heading = doc.add_heading('Background and Motivation', level=1)
    
    # First paragraph
    p1 = doc.add_paragraph()
    p1_text = "The Madden-Julian Oscillation (MJO) is the dominant mode of tropical intraseasonal variability, characterized by eastward-propagating large-scale coupled patterns of convection and circulation with periods of 30-90 days"
    p1.add_run(p1_text)
    add_superscript_reference(p1, 1)
    p1.add_run(",")
    add_superscript_reference(p1, 2)
    p1.add_run(". The MJO exerts profound influence on global weather patterns through complex teleconnections, affecting storm tracks, cyclone activity, atmospheric rivers, and blocking events across the Northern Hemisphere")
    add_superscript_reference(p1, 3)
    p1.add_run(",")
    add_superscript_reference(p1, 4)
    p1.add_run(",")
    add_superscript_reference(p1, 5)
    p1.add_run(". Understanding the coupling between the tropical troposphere and the global stratosphere via atmospheric gravity waves (GWs) is crucial for climate modeling and prediction, particularly for advancing LLNL's E3SM model capabilities and enhancing subseasonal-to-seasonal forecasting for national security, energy infrastructure planning, and emergency preparedness applications")
    add_superscript_reference(p1, 6)
    p1.add_run(",")
    add_superscript_reference(p1, 7)
    p1.add_run(".")
    
    # Second paragraph
    p2 = doc.add_paragraph()
    p2.add_run("The MJO's influence extends far beyond tropical regions through its modulation of extratropical weather patterns, making it a critical component of the global climate system that must be accurately represented in Earth system models like E3SM. MJO-related teleconnections can trigger or suppress extreme weather events, including heat waves, cold outbreaks, droughts, and flooding, with significant implications for agriculture, water resources, energy demand, and public safety")
    add_superscript_reference(p2, 3)
    p2.add_run(",")
    add_superscript_reference(p2, 4)
    p2.add_run(". The economic and societal impacts of MJO-related weather variability underscore the importance of improving E3SM's predictive capabilities on subseasonal-to-seasonal timescales, where traditional weather forecasting and seasonal climate prediction have limited skill.")
    
    return doc

def add_research_questions_section(doc):
    """Add the Proposed Research and Scientific Questions section"""
    
    # Research Questions
    rq_heading = doc.add_heading('Proposed Research and Scientific Questions', level=1)
    
    # Introduction paragraph
    intro_para = doc.add_paragraph()
    intro_para.add_run("This research addresses fundamental questions about stratosphere-troposphere coupling through gravity waves modulated by the MJO, with direct implications for improving climate prediction capabilities and advancing LLNL's E3SM model development. The research leverages LLNL's expertise in large-scale data analysis, computational modeling, Earth system science, and high-performance computing to provide critical observational constraints for next-generation climate models.")
    
    # Primary Research Question
    primary_heading = doc.add_heading('Primary Research Question:', level=2)
    
    primary_para = doc.add_paragraph()
    primary_para.add_run("How does the MJO modulate stratospheric gravity wave activity globally, what are the underlying physical mechanisms governing this relationship, and how can these observational insights improve E3SM's representation of stratosphere-troposphere coupling for enhanced climate predictability?")
    
    # Specific Scientific Questions
    specific_heading = doc.add_heading('Specific Scientific Questions:', level=2)
    
    questions = [
        "How do MJO-related changes in tropical convection translate into observable variations in stratospheric GW potential energy, momentum flux, and spectral characteristics across different geographical regions and seasons, and how well does E3SM capture these relationships?",
        "What is the relative importance of source modulation (MJO convection) versus propagation filtering (MJO-modulated background state) in explaining observed MJO-GW relationships, and how can this understanding improve E3SM's gravity wave parameterizations?",
        "How do different MJO propagation types (standing, jumping, slow/fast-propagating) influence the global distribution of stratospheric GW activity, and how does E3SM's representation compare with observations?",
        "How do the QBO and ENSO interactively modulate MJO-GW relationships, and what observational constraints can enhance E3SM's representation of these multi-scale interactions?",
        "How can comprehensive evaluation of E3SM's gravity wave parameterizations using global RO observations lead to improved model physics and enhanced subseasonal-to-seasonal prediction capabilities?",
        "What are the implications of improved MJO-GW representation in E3SM for stratospheric ozone distribution, surface climate prediction, and LLNL's mission-relevant applications?"
    ]
    
    for i, question in enumerate(questions, 1):
        q_para = doc.add_paragraph()
        q_para.add_run(f"{i}. {question}")
    
    return doc

def add_objectives_section(doc):
    """Add the Research Objectives section"""

    # Research Objectives
    obj_heading = doc.add_heading('Research Objectives', level=1)

    intro_para = doc.add_paragraph()
    intro_para.add_run("The research objectives are structured to address these questions systematically through three interconnected work packages that directly support LLNL's E3SM development and mission priorities:")

    # Objective 1
    obj1_heading = doc.add_heading('Objective 1: Establish Observational Baseline and E3SM Evaluation Framework (Months 1-12)', level=2)

    obj1_para = doc.add_paragraph()
    obj1_para.add_run("Develop and validate robust methodologies for extracting GW parameters from RO temperature profiles through comprehensive process-based validation using focused case studies and statistical comparison with independent satellite measurements (TIMED/SABER, Aura/MLS)")
    add_superscript_reference(obj1_para, 16)
    obj1_para.add_run(",")
    add_superscript_reference(obj1_para, 17)
    obj1_para.add_run(". Establish baseline E3SM performance in representing MJO-GW relationships using current model configurations. Develop advanced techniques for estimating GW momentum flux from both observations and E3SM output, including uncertainty quantification and quality control procedures. This objective creates the foundation for model-observation comparison and identifies key areas for E3SM improvement.")

    # Objective 2
    obj2_heading = doc.add_heading('Objective 2: Characterize MJO-GW Processes and Develop E3SM Enhancements (Months 8-20)', level=2)

    obj2_para = doc.add_paragraph()
    obj2_para.add_run("Create the first comprehensive global climatology of stratospheric GW activity modulated by the MJO using validated observational parameters. Categorize MJO events by propagation types following established methodologies")
    add_superscript_reference(obj2_para, 18)
    obj2_para.add_run(" and analyze corresponding E3SM performance for each type. Investigate interactive modulation by QBO and ENSO in both observations and E3SM, identifying specific model deficiencies. Develop and test improved gravity wave parameterizations for E3SM-EAM based on observational constraints, focusing on convective gravity wave sources and propagation filtering mechanisms.")

    # Objective 3
    obj3_heading = doc.add_heading('Objective 3: Implement and Validate E3SM Improvements for Enhanced Prediction (Months 15-24)', level=2)

    obj3_para = doc.add_paragraph()
    obj3_para.add_run("Implement improved parameterizations in E3SM and conduct comprehensive validation using the observational climatology as benchmark. Evaluate enhanced E3SM configurations using established metrics")
    add_superscript_reference(obj3_para, 19)
    obj3_para.add_run(" and assess improvements in subseasonal-to-seasonal prediction skill. Develop operational recommendations for E3SM gravity wave physics and provide community-ready parameterizations for broader adoption. This objective delivers tangible improvements to LLNL's flagship climate model while advancing fundamental understanding of stratosphere-troposphere coupling.")

    return doc

def add_methodology_section(doc):
    """Add the Methodology section"""

    # Methodology
    method_heading = doc.add_heading('Methodology', level=1)

    # Data Acquisition and Processing
    data_heading = doc.add_heading('Data Acquisition and Processing', level=2)

    data_intro = doc.add_paragraph()
    data_intro.add_run("The methodology integrates cutting-edge satellite data processing, advanced atmospheric dynamics diagnostics, machine learning techniques, and robust statistical analysis applied to the extensive multi-mission RO dataset (2006-2024) and complementary datasets. This comprehensive approach leverages LLNL's world-class computational resources, high-performance computing infrastructure, and expertise in large-scale data analysis to address fundamental questions about stratosphere-troposphere coupling and provide critical constraints for E3SM model development.")

    # Data sources
    data_sources = [
        "• Radio Occultation Data: Level 2 'dry' temperature profiles from COSMIC-1/2, MetOp, Spire, and other missions (2006-2024) totaling >6 million profiles globally. Data acquired from CDAAC, EUMETSAT, and commercial providers with comprehensive quality control and inter-mission calibration.",
        "• Reanalysis Data: ERA5 hourly/daily/monthly fields (winds, temperature, geopotential height, humidity, surface pressure) at 0.25° resolution for background state characterization and GW filtering analysis.",
        "• Climate Indices: Real-time Multivariate MJO Index (RMM1, RMM2), QBO index (ERA5 U50, U30), ENSO indices (Niño3.4, SOI), solar activity indices (F10.7 cm flux, sunspot number) for comprehensive climate mode analysis.",
        "• Satellite Observations: Outgoing Longwave Radiation (OLR) data from NOAA, precipitation data from TRMM/GPM, and complementary GW observations from TIMED/SABER and Aura/MLS for validation and cross-comparison.",
        "• E3SM Model Data: High-resolution E3SM-EAM output including 3D temperature, winds, convective heating rates, and parameterized GW momentum fluxes; specialized E3SM sensitivity experiments with modified gravity wave parameterizations; and E3SM ensemble simulations for uncertainty quantification.",
        "• CMIP6/CMIP7 Model Data: Daily outputs from multiple Earth system models including 3D temperature, winds, and GW momentum fluxes for inter-model comparison and E3SM benchmarking."
    ]

    for source in data_sources:
        source_para = doc.add_paragraph()
        source_para.add_run(source)

    return doc

if __name__ == "__main__":
    # Create the enhanced document
    doc = create_enhanced_proposal()
    doc = add_background_section(doc)
    doc = add_research_questions_section(doc)
    doc = add_objectives_section(doc)
    doc = add_methodology_section(doc)

    # Save the document
    doc.save('Enhanced_LLNL_MJO_Proposal_E3SM.docx')
    print("Enhanced LLNL proposal document created successfully!")
