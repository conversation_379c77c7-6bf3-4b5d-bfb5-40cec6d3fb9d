import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
from matplotlib.patches import Rectangle
import pandas as pd

def create_mjo_gantt_chart():
    """Create an academic-style Gantt chart for MJO project"""

    fig, ax = plt.subplots(figsize=(18, 10))

    # Define activities for MJO project
    activities = [
        # WP1 Activities - Data Preparation and Validation (Light Blue)
        {'code': 'WP1.1', 'name': 'Data acquisition & QC', 'start': 1, 'duration': 3, 'color': '#E3F2FD'},
        {'code': 'WP1.2', 'name': 'Multi-platform validation', 'start': 3, 'duration': 4, 'color': '#E3F2FD'},
        {'code': 'WP1.3', 'name': 'GW parameter extraction', 'start': 4, 'duration': 3, 'color': '#E3F2FD'},
        {'code': 'WP1.4', 'name': 'Global climatology dev.', 'start': 6, 'duration': 3, 'color': '#E3F2FD'},

        # WP2 Activities - MJO Analysis (Light Green)
        {'code': 'WP2.1', 'name': 'MJO composite analysis', 'start': 8, 'duration': 4, 'color': '#E8F5E8'},
        {'code': 'WP2.2', 'name': 'Propagation type class.', 'start': 10, 'duration': 3, 'color': '#E8F5E8'},
        {'code': 'WP2.3', 'name': 'Observational climatology', 'start': 11, 'duration': 3, 'color': '#E8F5E8'},

        # WP3 Activities - Model Evaluation (Light Orange)
        {'code': 'WP3.1', 'name': 'E3SM model evaluation', 'start': 13, 'duration': 4, 'color': '#FFF3E0'},
        {'code': 'WP3.2', 'name': 'CMIP6/CMIP7 analysis', 'start': 15, 'duration': 4, 'color': '#FFF3E0'},
        {'code': 'WP3.3', 'name': 'Bias identification', 'start': 17, 'duration': 3, 'color': '#FFF3E0'},

        # WP4 Activities - Integration and Synthesis (Light Purple)
        {'code': 'WP4.1', 'name': 'Uncertainty quantification', 'start': 17, 'duration': 4, 'color': '#F3E5F5'},
        {'code': 'WP4.2', 'name': 'Parameterization rec.', 'start': 20, 'duration': 3, 'color': '#F3E5F5'},
        {'code': 'WP4.3', 'name': 'Final synthesis', 'start': 22, 'duration': 3, 'color': '#F3E5F5'},

        # Project Management (Light Pink)
        {'code': 'PM', 'name': 'Project Management', 'start': 1, 'duration': 24, 'color': '#FCE4EC'},
    ]

    # Deliverables
    deliverables = [
        {'name': 'D1', 'month': 8, 'description': 'Validation Report & GW Climatology'},
        {'name': 'D2', 'month': 14, 'description': 'MJO-GW Observational Climatology'},
        {'name': 'D3', 'month': 20, 'description': 'Model Evaluation Report'},
        {'name': 'D4', 'month': 24, 'description': 'Final Project Report'}
    ]

    # Plotting
    y_positions = np.arange(len(activities))[::-1]

    for i, activity in enumerate(activities):
        y_pos = len(activities) - 1 - i
        rect = Rectangle((activity['start']-1, y_pos-0.35), activity['duration'], 0.7,
                        facecolor=activity['color'], edgecolor='black', linewidth=0.8)
        ax.add_patch(rect)
        
        # Add activity code in the center
        ax.text(activity['start'] - 1 + activity['duration']/2, y_pos,
                activity['code'], ha='center', va='center',
                fontsize=11, fontweight='bold', color='black')

    # Deliverable markers
    for deliv in deliverables:
        ax.scatter(deliv['month'], -0.8, s=150, marker='D',
                  color='red', edgecolor='black', linewidth=1, zorder=10)
        ax.text(deliv['month'], -1.2, deliv['name'],
               ha='center', va='top', fontsize=12, fontweight='bold', color='red')

    # Formatting
    ax.set_xlim(0, 25)
    ax.set_ylim(-1.8, len(activities) + 0.5)
    ax.set_ylabel('Work Packages', fontsize=16, fontweight='bold')
    
    # Create custom y-tick labels with activity names
    y_labels = [f"{a['code']}: {a['name']}" for a in activities]
    ax.set_yticks(y_positions)
    ax.set_yticklabels(y_labels, fontsize=12, fontweight='bold')

    # Set x-axis ticks for months at TOP
    months = np.arange(1, 25)
    ax.set_xticks(months)
    ax.set_xticklabels(
        [f'M{m}' for m in months],
        rotation=0,
        fontsize=12,
        fontweight='bold'
    )
    ax.xaxis.tick_top()
    ax.xaxis.set_label_position('top')
    
    ax.set_xlabel(
        'Project Timeline (Months)',
        fontsize=16,
        fontweight='bold',
        labelpad=20
    )

    ax.grid(True, axis='x', alpha=0.8, linestyle='-', linewidth=0.5)
    ax.set_axisbelow(True)

    # Add quarter markers
    for q in [6, 12, 18, 24]:
        ax.axvline(x=q, color='darkblue', linestyle='--', alpha=0.6, linewidth=2)
        ax.text(q, len(activities) + 0.2, f'Q{q//6}', ha='center', va='bottom',
                fontweight='bold', color='darkblue', fontsize=14,
                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', edgecolor='darkblue'))

    # # Add title
    # ax.set_title('MJO Stratospheric Gravity Waves Project - 24 Month Timeline',
    #             fontsize=18, fontweight='bold', pad=50)

    plt.tight_layout()
    plt.savefig('MJO_Project_Gantt_Chart.jpeg', dpi=300, bbox_inches='tight')
    plt.savefig('MJO_Project_Gantt_Chart.pdf', bbox_inches='tight')

    return fig, ax


if __name__ == "__main__":
    fig, ax = create_mjo_gantt_chart()
    plt.show()
