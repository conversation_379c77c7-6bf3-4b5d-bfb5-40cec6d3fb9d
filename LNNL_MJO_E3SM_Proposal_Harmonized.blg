This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: LNNL_MJO_E3SM_Proposal_Harmonized.aux
Reallocating 'name_of_file' (item size: 1) to 6 items.
The style file: unsrt.bst
Reallocating 'name_of_file' (item size: 1) to 20 items.
Database file #1: LNNL_MJO_References.bib
You've used 44 entries,
            1791 wiz_defined-function locations,
            705 strings with 13090 characters,
and the built_in function-call counts, 12914 in all, are:
= -- 1092
> -- 754
< -- 0
+ -- 267
- -- 223
* -- 1199
:= -- 2116
add.period$ -- 132
call.type$ -- 44
change.case$ -- 44
chr.to.int$ -- 0
cite$ -- 44
duplicate$ -- 396
empty$ -- 1139
format.name$ -- 223
if$ -- 2696
int.to.chr$ -- 0
int.to.str$ -- 44
missing$ -- 44
newline$ -- 223
num.names$ -- 44
pop$ -- 44
preamble$ -- 1
purify$ -- 0
quote$ -- 0
skip$ -- 143
stack$ -- 0
substring$ -- 1305
swap$ -- 44
text.length$ -- 0
text.prefix$ -- 0
top$ -- 0
type$ -- 0
warning$ -- 0
while$ -- 121
width$ -- 46
write$ -- 486
